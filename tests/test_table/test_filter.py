"""
Tests for the TableFilter class.

This module contains unit tests for the TableFilter class, which is responsible
for filtering and classifying tables from parsed documents.
"""

import json
from unittest.mock import MagicMock, patch

import pytest

from cneutral_doc.document.parser.base import ParsedDocument, Table
from cneutral_doc.table.base import TableClassificationResult
from cneutral_doc.table.classifier import GeminiTableClassifier
from cneutral_doc.table.filter import TableFilter


@pytest.fixture
def mock_classifier():
    """Create a mock classifier for testing."""
    mock = MagicMock(spec=GeminiTableClassifier)

    # Configure the mock to return a valid TableClassificationResult
    mock.classify_table.return_value = TableClassificationResult(
        e_score=0.7,
        s_score=0.1,
        g_score=0.1,
        other_score=0.1,
        numerical_density=0.8,
        rows=5,
        cols=5,
        is_valid_table=True,
        reasoning="Test reasoning",
        validation_message=None,
        validation_score=0.85,
    )

    return mock


@pytest.fixture
def sample_tables():
    """Create sample tables for testing."""
    return [
        Table(
            index=1,
            page_number=1,
            content="| Header 1 | Header 2 |\n|----------|----------|\n| Data 1 | Data 2 |",
        ),
        Table(
            index=2,
            page_number=2,
            content="| Header A | Header B |\n|----------|----------|\n| Data A | Data B |",
        ),
    ]


@pytest.fixture
def sample_document(sample_tables):
    """Create a sample parsed document with tables for testing."""
    doc = MagicMock(spec=ParsedDocument)
    doc.tables = sample_tables
    return doc


@pytest.fixture
def temp_output_dir(tmp_path):
    """Create a temporary directory for test outputs."""
    output_dir = tmp_path / "test_outputs"
    output_dir.mkdir()
    return output_dir


class TestTableFilter:
    """Test cases for the TableFilter class."""

    def test_init_with_valid_api_key(self):
        """Test initialization with a valid API key."""
        # Arrange & Act
        with patch(
            "cneutral_doc.table.filter.GeminiTableClassifier"
        ) as mock_classifier_class:
            table_filter = TableFilter(api_key="valid_api_key")

            # Assert
            assert table_filter is not None
            mock_classifier_class.assert_called_once_with(api_key="valid_api_key")

    def test_init_with_empty_api_key(self):
        """Test initialization with an empty API key raises ValueError."""
        # Arrange & Act & Assert
        with pytest.raises(ValueError, match="API key cannot be empty or None"):
            TableFilter(api_key="")

    def test_init_with_none_api_key(self):
        """Test initialization with None API key raises ValueError."""
        # Arrange & Act & Assert
        with pytest.raises(ValueError, match="API key cannot be empty or None"):
            TableFilter(api_key=None)  # type: ignore

    def test_init_with_custom_classifier(self):
        """Test initialization with a custom classifier."""
        # Arrange
        mock_classifier = MagicMock(spec=GeminiTableClassifier)

        # Act
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)

        # Assert
        assert table_filter.classifier is mock_classifier

    def test_filter_tables_with_none_document(self):
        """Test filter_tables with None document raises ValueError."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key")

        # Act & Assert
        with pytest.raises(ValueError, match="Document cannot be None"):
            table_filter.filter_tables(document=None)  # type: ignore

    def test_filter_tables_with_invalid_k(self):
        """Test filter_tables with invalid k value raises ValueError."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key")
        doc = MagicMock(spec=ParsedDocument)

        # Act & Assert
        with pytest.raises(ValueError, match="Parameter k must be a positive integer"):
            table_filter.filter_tables(document=doc, k=0)

        with pytest.raises(ValueError, match="Parameter k must be a positive integer"):
            table_filter.filter_tables(document=doc, k=-1)

    def test_filter_tables_with_empty_document(self, sample_document, mock_classifier):
        """Test filter_tables with document containing no tables."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)
        empty_doc = MagicMock(spec=ParsedDocument)
        empty_doc.tables = []

        # Act
        result = table_filter.filter_tables(document=empty_doc)

        # Assert
        assert result == {
            "tables": [],
            "classifications": {},
            "filter_scores": {"E": {}, "S": {}, "G": {}},
        }
        mock_classifier.classify_table.assert_not_called()

    def test_filter_tables_basic_functionality(self, sample_document, mock_classifier):
        """Test basic functionality of filter_tables."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)

        # Act
        result = table_filter.filter_tables(document=sample_document)

        # Assert
        assert "tables" in result
        assert "classifications" in result
        assert "filter_scores" in result
        assert len(result["tables"]) == 2
        assert "1" in result["classifications"]
        assert "2" in result["classifications"]
        assert mock_classifier.classify_table.call_count == 2

    def test_filter_tables_with_save_path(
        self, sample_document, mock_classifier, temp_output_dir
    ):
        """Test filter_tables with save_path parameter."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)
        save_path = temp_output_dir / "test_results.json"

        # Act
        result = table_filter.filter_tables(
            document=sample_document, save_path=str(save_path)
        )

        # Assert
        assert save_path.exists()
        with open(save_path, "r") as f:
            saved_data = json.load(f)
        assert "tables" in saved_data
        assert "classifications" in saved_data
        assert len(saved_data["tables"]) == 2

    def test_filter_tables_with_k_parameter(self, sample_document, mock_classifier):
        """Test filter_tables with k parameter."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)

        # Act
        result = table_filter.filter_tables(document=sample_document, k=1)

        # Assert
        assert "E" in result
        assert "S" in result
        assert "G" in result
        assert isinstance(result["E"], list)
        assert isinstance(result["S"], list)
        assert isinstance(result["G"], list)
        # Since our mock classifier returns high E score, we should have entries in E category
        assert len(result["E"]) > 0

    def test_classification_error_handling(self, sample_document):
        """Test handling of classification errors."""
        # Arrange
        mock_classifier = MagicMock(spec=GeminiTableClassifier)
        mock_classifier.classify_table.side_effect = Exception("Classification error")
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)

        # Act & Assert - Updated to match new error message format
        with pytest.raises(RuntimeError, match="All tables failed during classification call"):
            table_filter.filter_tables(document=sample_document)

    def test_partial_classification_errors(self, sample_document):
        """Test handling of partial classification errors."""
        # Arrange
        mock_classifier = MagicMock(spec=GeminiTableClassifier)
        # First call raises exception, second call succeeds
        mock_classifier.classify_table.side_effect = [
            Exception("Classification error"),
            TableClassificationResult(
                e_score=0.7,
                s_score=0.1,
                g_score=0.1,
                other_score=0.1,
                numerical_density=0.8,
                rows=5,
                cols=5,
                is_valid_table=True,
                reasoning="Test reasoning",
                validation_message=None,
                validation_score=0.85,
            ),
        ]
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)

        # Act
        result = table_filter.filter_tables(document=sample_document)

        # Assert
        assert "tables" in result
        assert "classifications" in result
        assert "1" in result["classifications"]
        assert "2" in result["classifications"]
        assert "error" in result["classifications"]["1"]

    def test_save_results_error_handling(
        self, sample_document, mock_classifier, temp_output_dir
    ):
        """Test error handling when saving results."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)
        invalid_path = temp_output_dir / "nonexistent_dir" / "test_results.json"

        # Act & Assert - This should not raise an exception as we create parent directories
        result = table_filter.filter_tables(
            document=sample_document, save_path=str(invalid_path)
        )

        # Verify the file was created
        assert invalid_path.exists()

    def test_select_top_k_with_invalid_k(self, sample_document, mock_classifier):
        """Test _select_top_k with invalid k value."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)
        table_filter.filter_tables(document=sample_document)

        # Act & Assert
        with pytest.raises(ValueError, match="Parameter k must be a positive integer"):
            table_filter._select_top_k(k=0)

        with pytest.raises(ValueError, match="Parameter k must be a positive integer"):
            table_filter._select_top_k(k=-1)

    def test_select_top_k_without_prior_filtering(self):
        """Test _select_top_k without prior filtering."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key")

        # Act
        result = table_filter._select_top_k(k=1)

        # Assert
        assert result == {"E": [], "S": [], "G": []}

    def test_select_top_k_with_different_k_values(
        self, sample_document, mock_classifier
    ):
        """Test _select_top_k with different k values."""
        # Arrange
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)
        table_filter.filter_tables(document=sample_document)

        # Act
        result_k1 = table_filter._select_top_k(k=1)
        result_k2 = table_filter._select_top_k(k=2)

        # Assert
        assert len(result_k1["E"]) == 1
        assert len(result_k2["E"]) == 2
        # Since we have only 2 tables, k=2 should include all tables that passed filtering
        assert len(result_k2["E"]) == 2

    def test_filter_tables_with_proper_classification_results(self, sample_document):
        """Test filter_tables with proper TableClassificationResult objects."""
        # Arrange
        mock_classifier = MagicMock(spec=GeminiTableClassifier)
        # Return a proper TableClassificationResult object
        mock_classifier.classify_table.return_value = TableClassificationResult(
            e_score=0.7,
            s_score=0.1,
            g_score=0.1,
            other_score=0.1,
            numerical_density=0.8,
            rows=5,
            cols=5,
            is_valid_table=True,
            reasoning="Test reasoning",
            validation_message=None,
            validation_score=0.85,
        )
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)

        # Act
        result = table_filter.filter_tables(document=sample_document)

        # Assert
        assert "tables" in result
        assert "classifications" in result
        assert "filter_scores" in result
        assert "E" in result["filter_scores"]
        assert len(result["filter_scores"]["E"]) > 0
        # Verify that validation scores are included
        for table_id, classification in result["classifications"].items():
            assert hasattr(classification, 'validation_score')
            assert classification.validation_score is not None

    def test_filter_tables_with_low_numerical_density(self, sample_document):
        """Test filter_tables with low numerical density tables."""
        # Arrange
        mock_classifier = MagicMock(spec=GeminiTableClassifier)
        mock_classifier.classify_table.return_value = TableClassificationResult(
            e_score=0.7,
            s_score=0.1,
            g_score=0.1,
            other_score=0.1,
            numerical_density=0.2,  # Below 0.3 threshold
            rows=5,
            cols=5,
            is_valid_table=True,
            reasoning="Test reasoning",
            validation_message=None,
            validation_score=0.85,
        )
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)

        # Act
        result = table_filter.filter_tables(document=sample_document)

        # Assert
        assert "filter_scores" in result
        # All tables should be filtered out due to low numerical density
        assert len(result["filter_scores"]["E"]) == 0
        assert len(result["filter_scores"]["S"]) == 0
        assert len(result["filter_scores"]["G"]) == 0

    def test_filter_tables_with_other_as_highest_category(self, sample_document):
        """Test filter_tables with 'Other' as the highest scoring category."""
        # Arrange
        mock_classifier = MagicMock(spec=GeminiTableClassifier)
        mock_classifier.classify_table.return_value = TableClassificationResult(
            e_score=0.1,
            s_score=0.1,
            g_score=0.1,
            other_score=0.7,  # 'Other' is highest
            numerical_density=0.8,
            rows=5,
            cols=5,
            is_valid_table=True,
            reasoning="Test reasoning",
            validation_message=None,
            validation_score=0.85,
        )
        table_filter = TableFilter(api_key="valid_api_key", classifier=mock_classifier)

        # Act
        result = table_filter.filter_tables(document=sample_document)

        # Assert
        assert "filter_scores" in result
        # All tables should be filtered out as 'Other' is the highest category
        assert len(result["filter_scores"]["E"]) == 0
        assert len(result["filter_scores"]["S"]) == 0
        assert len(result["filter_scores"]["G"]) == 0
