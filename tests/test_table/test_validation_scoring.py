"""
Tests for the new validation scoring functionality.

This module contains comprehensive tests for the structure-based validation scoring
system that was added to improve table filtering flexibility.
"""

import pytest
from unittest.mock import MagicMock

from cneutral_doc.table.base import TableClassificationResult
from cneutral_doc.table.classifier import LLMTableClassifier


class MockValidationTableClassifier(LLMTableClassifier):
    """Concrete implementation of LLMTableClassifier for validation testing."""

    def _setup_chat_model(self):
        """Implement the abstract method for testing."""
        return MagicMock()


class TestValidationScoring:
    """Tests for the validation scoring system."""

    @pytest.fixture
    def classifier(self):
        """Create a classifier for validation testing."""
        return MockValidationTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )

    def test_validation_score_range(self, classifier):
        """Test that validation scores are always between 0.0 and 1.0."""
        test_tables = [
            # Well-formed table
            """|Header 1|Header 2|Header 3|
|---------|---------|---------|
|Data 1   |Data 2   |Data 3   |
|Value 1  |Value 2  |Value 3  |""",
            
            # Malformed table
            "|Header 1|Header 2\n|Data 1|Data 2|Data 3",
            
            # Empty-ish table
            "|||\n|---|---|---|\n|||",
            
            # Table with inconsistent columns
            """|A|B|C|
|---|---|
|1|2|3|
|4|5|6|7|""",
        ]
        
        for table in test_tables:
            result = classifier.validate_table(table)
            assert 0.0 <= result["validation_score"] <= 1.0
            assert isinstance(result["validation_score"], float)

    def test_validation_score_components(self, classifier):
        """Test that validation scores reflect table quality."""
        # High quality table should have high score
        good_table = """|Year|Revenue|Profit|Employees|
|-----|-------|------|---------|
|2020 |100M   |10M   |1000     |
|2021 |120M   |15M   |1200     |
|2022 |140M   |20M   |1400     |"""
        
        good_result = classifier.validate_table(good_table)
        assert good_result["is_valid"] is True
        assert good_result["validation_score"] > 0.6  # Above threshold
        
        # Poor quality table should have lower score
        bad_table = "|A|B\n|1|2|3|4|5"
        
        bad_result = classifier.validate_table(bad_table)
        assert bad_result["is_valid"] is False
        assert bad_result["validation_score"] < 0.6  # Below threshold

    def test_numerical_density_calculation(self, classifier):
        """Test that numerical density is calculated correctly."""
        # Table with high numerical content
        numeric_table = """|Year|Count|Percentage|
|-----|-----|----------|
|2020 |100  |25.5      |
|2021 |150  |37.8      |
|2022 |200  |50.0      |"""
        
        result = classifier.validate_table(numeric_table)
        assert result["numerical_density"] > 0.5  # Should be high
        
        # Table with low numerical content
        text_table = """|Name|Department|Role|
|-----|----------|-----|
|John |Sales     |Manager|
|Jane |Marketing |Director|
|Bob  |IT        |Developer|"""
        
        result = classifier.validate_table(text_table)
        assert result["numerical_density"] < 0.3  # Should be low

    def test_dimension_calculation(self, classifier):
        """Test that table dimensions are calculated correctly."""
        table = """|A|B|C|D|
|---|---|---|---|
|1|2|3|4|
|5|6|7|8|
|9|10|11|12|"""
        
        result = classifier.validate_table(table)
        rows, cols = result["dimensions"]
        assert rows == 4  # Header + 3 data rows
        assert cols == 4  # 4 columns

    def test_validation_error_messages(self, classifier):
        """Test that validation error messages are informative."""
        # Test various failure cases
        test_cases = [
            ("", "empty"),  # Empty string
            ("Not a table at all", "pipe"),  # No pipes
            ("|A|\n|B|C|D|", "inconsistent"),  # Inconsistent structure
        ]
        
        for table, expected_error_type in test_cases:
            if table == "":
                with pytest.raises(ValueError, match="Input table cannot be empty"):
                    classifier.validate_table(table)
            else:
                result = classifier.validate_table(table)
                assert result["is_valid"] is False
                assert result["error_message"] is not None
                assert len(result["error_message"]) > 0

    def test_validation_threshold_behavior(self, classifier):
        """Test that the validation threshold works correctly."""
        # Create a table that should be right at the threshold
        borderline_table = """|A|B|
|---|---|
|1|2|
|3|4|"""
        
        result = classifier.validate_table(borderline_table)
        # The result should be consistent with the threshold (0.6)
        if result["validation_score"] >= 0.6:
            assert result["is_valid"] is True
        else:
            assert result["is_valid"] is False

    def test_validation_score_in_classification_result(self, classifier):
        """Test that validation scores are included in classification results."""
        # Mock the LLM chain to avoid actual API calls
        classifier.chain = MagicMock()
        from cneutral_doc.table.classifier import ESGScores
        classifier.chain.invoke.return_value = ESGScores(
            reasoning="Test reasoning",
            E=0.8,
            S=0.1,
            G=0.1,
            O=0.0
        )
        
        table = """|Environmental Data|Value|
|-------------------|-----|
|CO2 Emissions      |100  |
|Energy Usage       |200  |"""
        
        result = classifier.classify_table(table)
        assert isinstance(result, TableClassificationResult)
        assert result.validation_score is not None
        assert 0.0 <= result.validation_score <= 1.0

    def test_edge_case_tables(self, classifier):
        """Test validation with edge case table formats."""
        edge_cases = [
            # Single cell table
            "|A|\n|---|",
            
            # Table with empty cells
            """|A|B|C|
|---|---|---|
|1||3|
||2||""",
            
            # Table with special characters
            """|Symbol|Value|
|------|-----|
|@#$%  |100  |
|&*()  |200  |""",
            
            # Very wide table
            "|" + "|".join([f"Col{i}" for i in range(20)]) + "|\n" +
            "|" + "|".join(["---" for _ in range(20)]) + "|\n" +
            "|" + "|".join([f"Val{i}" for i in range(20)]) + "|",
        ]
        
        for table in edge_cases:
            result = classifier.validate_table(table)
            # Should not crash and should return valid structure
            assert "validation_score" in result
            assert "is_valid" in result
            assert "dimensions" in result
            assert "numerical_density" in result
