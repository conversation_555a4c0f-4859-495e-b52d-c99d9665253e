"""
Performance tests for the table validation and filtering system.

This module contains tests to ensure that the new validation scoring
system doesn't significantly impact processing performance.
"""

import time
import pytest
from unittest.mock import MagicMock

from cneutral_doc.document.parser.base import ParsedDocument, Table
from cneutral_doc.table.base import TableClassificationResult
from cneutral_doc.table.filter import TableFilter
from cneutral_doc.table.classifier import LLMTableClassifier


class MockPerformanceTableClassifier(LLMTableClassifier):
    """Mock classifier for performance testing."""

    def _setup_chat_model(self):
        """Implement the abstract method for testing."""
        return MagicMock()


class TestPerformance:
    """Performance tests for the validation and filtering system."""

    @pytest.fixture
    def classifier(self):
        """Create a classifier for performance testing."""
        return MockPerformanceTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )

    @pytest.fixture
    def large_table(self):
        """Create a large table for performance testing."""
        headers = "|" + "|".join([f"Col{i}" for i in range(20)]) + "|"
        separator = "|" + "|".join(["---" for _ in range(20)]) + "|"
        
        rows = []
        for i in range(100):  # 100 data rows
            row = "|" + "|".join([f"Data{i}_{j}" for j in range(20)]) + "|"
            rows.append(row)
        
        return "\n".join([headers, separator] + rows)

    @pytest.fixture
    def many_small_tables(self):
        """Create many small tables for performance testing."""
        tables = []
        for i in range(50):  # 50 small tables
            table = f"""|Header A|Header B|Header C|
|---------|---------|---------|
|Data {i}A|Data {i}B|Data {i}C|
|Val {i}A |Val {i}B |Val {i}C |"""
            tables.append(Table(
                index=i+1,
                page_number=1,
                content=table
            ))
        return tables

    def test_validation_performance_single_large_table(self, classifier, large_table):
        """Test validation performance with a single large table."""
        # Warm up
        classifier.validate_table(large_table)
        
        # Measure performance
        start_time = time.time()
        result = classifier.validate_table(large_table)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Validation should complete quickly (under 1 second for large table)
        assert processing_time < 1.0, f"Validation took {processing_time:.3f}s, expected < 1.0s"
        
        # Result should be valid
        assert "validation_score" in result
        assert "is_valid" in result
        assert "dimensions" in result

    def test_validation_performance_many_small_tables(self, classifier):
        """Test validation performance with many small tables."""
        small_table = """|A|B|C|
|---|---|---|
|1|2|3|
|4|5|6|"""
        
        num_tables = 100
        
        # Warm up
        classifier.validate_table(small_table)
        
        # Measure performance
        start_time = time.time()
        for _ in range(num_tables):
            result = classifier.validate_table(small_table)
            assert "validation_score" in result
        end_time = time.time()
        
        processing_time = end_time - start_time
        avg_time_per_table = processing_time / num_tables
        
        # Should process small tables very quickly
        assert avg_time_per_table < 0.01, f"Average time per table: {avg_time_per_table:.4f}s, expected < 0.01s"

    def test_filtering_pipeline_performance(self, many_small_tables):
        """Test performance of the complete filtering pipeline."""
        # Create document with many tables
        doc = MagicMock(spec=ParsedDocument)
        doc.tables = many_small_tables
        
        # Create mock classifier
        mock_classifier = MagicMock()
        mock_classifier.classify_table.return_value = TableClassificationResult(
            e_score=0.6, s_score=0.2, g_score=0.2, other_score=0.0,
            numerical_density=0.5, rows=3, cols=3, is_valid_table=True,
            reasoning="Performance test", validation_score=0.75
        )
        
        # Create filter
        table_filter = TableFilter(api_key="test-key", classifier=mock_classifier)
        
        # Measure performance
        start_time = time.time()
        result = table_filter.filter_tables(document=doc)
        end_time = time.time()
        
        processing_time = end_time - start_time
        avg_time_per_table = processing_time / len(many_small_tables)
        
        # Pipeline should be reasonably fast
        assert processing_time < 5.0, f"Pipeline took {processing_time:.3f}s for {len(many_small_tables)} tables"
        assert avg_time_per_table < 0.1, f"Average time per table: {avg_time_per_table:.4f}s"
        
        # Verify results
        assert len(result["tables"]) == len(many_small_tables)
        assert len(result["classifications"]) == len(many_small_tables)

    def test_validation_score_calculation_performance(self, classifier):
        """Test performance of validation score calculation specifically."""
        # Test tables of varying complexity
        test_tables = [
            # Simple table
            """|A|B|
|---|---|
|1|2|""",
            
            # Medium table
            """|A|B|C|D|E|
|---|---|---|---|---|
|1|2|3|4|5|
|6|7|8|9|10|
|11|12|13|14|15|""",
            
            # Complex table with mixed content
            """|Metric|Q1 2023|Q2 2023|Q3 2023|Q4 2023|YoY Change|
|------|-------|-------|-------|-------|----------|
|Revenue ($M)|125.5|134.2|142.8|156.3|+12.5%|
|Employees|1,250|1,285|1,320|1,355|+8.4%|
|Offices|15|16|16|17|+13.3%|
|Countries|8|8|9|9|+12.5%|""",
        ]
        
        total_time = 0
        for table in test_tables:
            start_time = time.time()
            result = classifier.validate_table(table)
            end_time = time.time()
            
            table_time = end_time - start_time
            total_time += table_time
            
            # Each validation should be very fast
            assert table_time < 0.1, f"Validation took {table_time:.4f}s for table"
            assert "validation_score" in result
        
        avg_time = total_time / len(test_tables)
        assert avg_time < 0.05, f"Average validation time: {avg_time:.4f}s"

    def test_memory_usage_stability(self, classifier):
        """Test that validation doesn't cause memory leaks."""
        import gc
        
        # Simple table for repeated validation
        table = """|Year|Value|
|-----|-----|
|2020|100|
|2021|110|
|2022|120|"""
        
        # Force garbage collection
        gc.collect()
        
        # Run many validations
        for i in range(1000):
            result = classifier.validate_table(table)
            assert result["is_valid"] is True
            
            # Periodically force garbage collection
            if i % 100 == 0:
                gc.collect()
        
        # Final garbage collection
        gc.collect()
        
        # Test should complete without memory issues
        # (If there were memory leaks, this test would likely fail or be very slow)

    @pytest.mark.parametrize("table_size", [
        (5, 5),    # Small table
        (20, 10),  # Medium table
        (50, 15),  # Large table
        (100, 20), # Very large table
    ])
    def test_validation_scales_with_table_size(self, classifier, table_size):
        """Test that validation performance scales reasonably with table size."""
        rows, cols = table_size
        
        # Generate table of specified size
        headers = "|" + "|".join([f"Col{i}" for i in range(cols)]) + "|"
        separator = "|" + "|".join(["---" for _ in range(cols)]) + "|"
        
        data_rows = []
        for i in range(rows):
            row = "|" + "|".join([f"D{i}_{j}" for j in range(cols)]) + "|"
            data_rows.append(row)
        
        table = "\n".join([headers, separator] + data_rows)
        
        # Measure validation time
        start_time = time.time()
        result = classifier.validate_table(table)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Time should scale reasonably (not exponentially)
        # For the largest table (100x20), should still be under 0.5 seconds
        max_expected_time = 0.001 * rows * cols / 10  # Very generous scaling
        max_expected_time = min(max_expected_time, 0.5)  # Cap at 0.5 seconds
        
        assert processing_time < max_expected_time, \
            f"Table {rows}x{cols} took {processing_time:.4f}s, expected < {max_expected_time:.4f}s"
        
        # Verify result is valid
        assert "validation_score" in result
        assert result["dimensions"] == (rows + 1, cols)  # +1 for header
