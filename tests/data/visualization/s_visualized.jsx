function SPlot() {
  const employmentData = [
    { disclosure: "401-1 New employee hires and employee turnover", location: "SR: Pg 21-22" },
    { disclosure: "401-2 Benefits provided to full-time employees that are not provided to temporary or part-time employees", location: "SR: Pg 21" },
    { disclosure: "401-3 Parental leave", location: "SR: Pg 21" },
  ];

  const safetyData = [
    { disclosure: "403-1 Occupational health and safety management system", location: "SR: Pg 24-25" },
    { disclosure: "403-2 Hazard identification, risk assessment, and incident investigation", location: "SR: Pg 24-25" },
    { disclosure: "403-4 Worker participation, consultation, and communication on occupational health and safety", location: "SR: Pg 24-25" },
    { disclosure: "403-5 Worker training on occupational health and safety", location: "SR: Pg 24-25" },
    { disclosure: "403-6 Promotion of worker health", location: "SR: Pg 24-25" },
    { disclosure: "403-7 Prevention and mitigation of occupational health and safety impacts directly linked by business relationships", location: "SR: Pg 24-25" },
  ];

  const employmentTraces = employmentData.map((item, index) => ({
    x: [item.disclosure],
    y: [1], // Dummy value since we're only displaying the disclosure and location
    text: [`Location: ${item.location}`],
    type: 'bar',
    name: `Disclosure ${index + 1}`,
    hoverinfo: 'x+text',
    marker: {
      color: '#2ca02c'
    },
  }));
  
  const safetyTraces = safetyData.map((item, index) => ({
    x: [item.disclosure],
    y: [1], // Dummy value since we're only displaying the disclosure and location
    text: [`Location: ${item.location}`],
    type: 'bar',
    name: `Disclosure ${index + 1}`,
    hoverinfo: 'x+text',
    marker: {
      color: '#1f77b4'
    },
  }));

  const employmentLayout = {
    title: 'Employment Disclosures',
    xaxis: {
      title: 'Disclosure',
      automargin: true,
      tickangle: -45,
    },
    yaxis: {
      title: 'Count',
      showticklabels: false,
      zeroline: false,
      showgrid: false
    },
    margin: { l: 50, r: 50, b: 200, t: 80 },
    height: 600,
    responsive: true
  };

  const safetyLayout = {
    title: 'Occupational Health and Safety Disclosures',
    xaxis: {
      title: 'Disclosure',
      automargin: true,
      tickangle: -45,
    },
    yaxis: {
      title: 'Count',
      showticklabels: false,
      zeroline: false,
      showgrid: false
    },
    margin: { l: 50, r: 50, b: 200, t: 80 },
    height: 600,
    responsive: true
  };
  
  return (
    <div>
      {typeof Plotly !== 'undefined' ? (
        <>
          <Plotly
            data={employmentTraces}
            layout={employmentLayout}
            style={{ width: "100%" }}
            useResizeHandler={true}
          />
          <Plotly
            data={safetyTraces}
            layout={safetyLayout}
            style={{ width: "100%" }}
            useResizeHandler={true}
          />
        </>
      ) : (
        <div>Plotly is not defined. Please ensure Plotly.js is correctly imported.</div>
      )}
    </div>
  );
}
